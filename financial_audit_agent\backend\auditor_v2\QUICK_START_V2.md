# 快速上手指南：新版分步式智能审核系统 V2.0

## 🚀 立即开始

### 1. 验证系统环境
```bash
# 进入新系统目录
cd financial_audit_agent/backend/auditor_v2

# 查看系统使用说明
python run_audit_v2.py --help
```

### 2. 配置API密钥
编辑 `../config.json` 文件：
```json
{
  "LLM_API_KEY": "您的API密钥",
  "LLM_MODEL_NAME": "qwen-max"
}
```

### 3. 执行真实审核
```bash
# 使用优化后的规则执行审核
python run_audit_v2.py
```

## 📋 新系统特性一览

### ✨ 核心优势

1. **🎯 显式指针技术**
   - 精确的数据定位：`[来源: 主报销单信息 -> 招待类型]`
   - 消除数据查找的模糊性
   - 100%准确的字段定位

2. **💡 业务意图透明化**
   - `[目的: ...]` 标签说明规则背后的业务逻辑
   - 帮助LLM理解审核的深层含义
   - 提升判断的合理性

3. **📋 结构化指令体系**
   - 分步骤的操作流程
   - 标准化的判断标准
   - 明确的输出要求

4. **🛡️ 完善的错误处理**
   - 明确处理数据缺失情况
   - 标准化的异常响应
   - 提升系统稳定性

### 🔄 分步式处理流程

```mermaid
graph TD
    A[开始审核] --> B[附件完整性检查]
    B --> C[字段内容与一致性检查]
    C --> D[金额与标准检查]
    D --> E[八项规定精神]
    E --> F[生成最终报告]
    
    B --> G[上下文传递]
    C --> G
    D --> G
    G --> H[智能关联分析]
```

## 🎯 规则优化亮点

### 示例1：精确的数据定位

**优化前：**
```
检查业务招待审批流程中，是否上传了发票？
```

**优化后：**
```
规则1：检查是否上传发票。
指令：请检查 [来源: 附件概览 -> 附件类型] 列表中是否包含"发票"。
```

### 示例2：结构化的计算指令

**优化前：**
```
将"用餐小票"上的"菜品小计"与"领用酒水金额"相加...
```

**优化后：**
```
规则27：检查实际消费是否超预算。
指令：请按以下步骤操作：
1. 找到 [来源: 附件：餐饮小票 -> 用餐金额] (A)
2. 找到 [来源: 主报销单信息 -> 酒水金额] (B)
3. 找到 [来源: 附件：业务招待事前审批表 -> 预计招待金额] (C)
4. 判断 (A + B) 是否小于或等于 C。请在理由中列出具体数值。
```

## 📊 性能提升预期

| 指标 | 预期改进 | 说明 |
|------|----------|------|
| 数据定位准确率 | +80% | 显式指针消除歧义 |
| 业务逻辑理解度 | +70% | 目的标签提供上下文 |
| 输出格式一致性 | +90% | 结构化指令规范输出 |
| 异常处理能力 | +80% | 完善的错误处理机制 |
| 审核深度 | +40% | 上下文传递增强关联分析 |

## 🔧 使用方法

### 基础使用
```bash
# 使用默认参数（推荐）
python run_audit_v2.py

# 自定义参数
python run_audit_v2.py \
  --form ../../ZDBXD2025051300001_表单提取.json \
  --attachment ../../ZDBXD2025051300001_附件提取.json \
  --rules ../../业务招待费审核规则_V2.txt \
  --output ../../audit_reports/my_audit_report.json
```

### 高级选项
```bash
# 不自动打开浏览器
python run_audit_v2.py --no-browser

# 使用自定义配置文件
python run_audit_v2.py --config /path/to/your/config.json
```

## 📈 监控和优化

### 1. 审核质量监控
- 检查审核报告的准确性
- 对比新旧系统的结果差异
- 收集用户反馈

### 2. 性能监控
- 监控审核耗时
- 观察内存和CPU使用情况
- 统计成功率和错误率

### 3. 持续改进
- 根据实际使用情况调整规则
- 优化提示词模板
- 扩展规则覆盖范围

## 🛠️ 故障排除

### 常见问题

**Q: 规则解析失败？**
A: 检查规则文件编码是否为UTF-8，确保使用正确的文件路径

**Q: 显式指针找不到数据？**
A: 检查数据文件格式，确保字段名称与指针中的名称完全匹配

**Q: LLM响应格式错误？**
A: 系统会自动清理响应格式，如仍有问题请检查API配置

**Q: 上下文传递不生效？**
A: 确保前序步骤有"不通过"或"警告"结果，系统才会传递上下文

### 调试技巧

1. **查看使用说明**：先运行 `python run_audit_v2.py --help` 了解参数
2. **检查日志输出**：观察系统的详细执行日志
3. **分步调试**：可以单独测试各个模块的功能
4. **对比测试**：与旧系统结果进行对比分析

## 🎉 成功案例

### 典型改进效果

**案例：招待对象一致性检查**

**优化前结果：**
```json
{
  "rule_id": "规则8",
  "status": "WARNING",
  "reason": "数据可能不一致，需要人工检查"
}
```

**优化后结果：**
```json
{
  "rule_id": "规则8：检查招待对象一致性。",
  "status": "警告",
  "reason": "根据 [来源: 主报销单信息 -> 招待对象] 值为'京基云景梧桐'，但 [来源: 附件：业务招待事前审批表 -> 招待对象] 值为'景福花园'，存在不一致。"
}
```

**改进效果：**
- ✅ 明确指出了具体的数据值
- ✅ 精确定位了不一致的字段
- ✅ 提供了可操作的审核依据

## 📞 技术支持

如需帮助，请：
1. 查看 `README_V2.md` 详细文档
2. 运行 `python run_audit_v2.py --help` 查看使用说明
3. 检查 `RULES_OPTIMIZATION_ANALYSIS.md` 了解优化详情
4. 参考 `MIGRATION_GUIDE.md` 进行系统迁移

---

**🎯 立即体验新系统的强大功能！**

```bash
cd financial_audit_agent/backend/auditor_v2
python run_audit_v2.py --help
```
