#!/usr/bin/env python3
"""
审核状态调试工具
用于诊断审核流程中的状态问题
"""

import json
import os
import time
from datetime import datetime

def read_audit_state():
    """读取当前审核状态"""
    state_file = "audit_state.json"
    if os.path.exists(state_file):
        try:
            with open(state_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"❌ 读取状态文件失败: {e}")
            return None
    else:
        print("❌ 状态文件不存在")
        return None

def analyze_audit_state(state):
    """分析审核状态"""
    if not state:
        return
    
    print("=" * 80)
    print("🔍 审核状态分析")
    print("=" * 80)
    
    # 基本信息
    print(f"📋 审核ID: {state.get('audit_id', 'N/A')}")
    print(f"📊 审核状态: {state.get('audit_status', 'N/A')}")
    print(f"🔄 当前阶段: {state.get('current_phase', 'N/A')}")
    print(f"📈 进度百分比: {state.get('progress_percent', 0)}%")
    
    # 时间信息
    start_time = state.get('start_time')
    if start_time:
        print(f"⏰ 开始时间: {start_time}")
        
    last_updated = state.get('last_updated')
    if last_updated:
        print(f"🔄 最后更新: {last_updated}")
        
        # 计算距离最后更新的时间
        try:
            last_update_dt = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
            now = datetime.now().astimezone()
            time_diff = now - last_update_dt
            print(f"⏱️  距离最后更新: {time_diff.total_seconds():.1f} 秒")
        except Exception as e:
            print(f"⚠️ 时间计算错误: {e}")
    
    # 消息信息
    message = state.get('message', '')
    detail = state.get('detail', '')
    print(f"💬 当前消息: {message}")
    print(f"📝 详细信息: {detail}")
    
    # 统计信息
    summary = state.get('summary', {})
    print("\n📊 规则统计:")
    print(f"   总规则数: {summary.get('total_rules', 0)}")
    print(f"   已完成: {summary.get('completed_rules', 0)}")
    print(f"   通过: {summary.get('passed_rules', 0)}")
    print(f"   失败: {summary.get('failed_rules', 0)}")
    print(f"   警告: {summary.get('warning_rules', 0)}")
    
    # AI思考过程分析
    ai_thinking = state.get('ai_thinking', '')
    if ai_thinking:
        print(f"\n🤖 AI思考过程长度: {len(ai_thinking)} 字符")
        
        # 分析思考过程中的阶段信息
        phases_found = []
        phase_keywords = [
            "附件完整性检查",
            "字段内容与一致性检查",
            "字段一致性检查",
            "金额与标准检查",
            "八项规定精神",
            "合规性检查"
        ]
        
        for keyword in phase_keywords:
            if keyword in ai_thinking:
                phases_found.append(keyword)
        
        print(f"🔍 思考过程中发现的阶段: {', '.join(phases_found) if phases_found else '无'}")
        
        # 显示思考过程的开头和结尾
        print(f"\n🤖 思考过程开头 (前200字符):")
        print("-" * 40)
        print(ai_thinking[:200])
        print("-" * 40)
        
        print(f"\n🤖 思考过程结尾 (后200字符):")
        print("-" * 40)
        print(ai_thinking[-200:])
        print("-" * 40)
    else:
        print("\n🤖 AI思考过程: 无")

def check_potential_issues(state):
    """检查潜在问题"""
    if not state:
        return
        
    print("\n" + "=" * 80)
    print("⚠️  潜在问题检查")
    print("=" * 80)
    
    issues = []
    
    # 检查状态一致性
    audit_status = state.get('audit_status', '')
    current_phase = state.get('current_phase', '')
    progress_percent = state.get('progress_percent', 0)
    
    # 问题1: 状态与阶段不匹配
    if audit_status == 'running' and current_phase == 'field-consistency' and progress_percent == 60:
        if '附件完整性检查' in state.get('ai_thinking', ''):
            issues.append("🔴 状态不一致: 显示在字段一致性阶段(60%)，但AI思考还在附件完整性检查")
    
    # 问题2: 长时间无更新
    last_updated = state.get('last_updated')
    if last_updated:
        try:
            last_update_dt = datetime.fromisoformat(last_updated.replace('Z', '+00:00'))
            now = datetime.now().astimezone()
            time_diff = now - last_update_dt
            if time_diff.total_seconds() > 300:  # 5分钟
                issues.append(f"🔴 长时间无更新: 距离最后更新已过去 {time_diff.total_seconds():.1f} 秒")
        except:
            pass
    
    # 问题3: 进度停滞
    if audit_status == 'running' and progress_percent > 0:
        summary = state.get('summary', {})
        if summary.get('total_rules', 0) == 0:
            issues.append("🔴 进度异常: 显示有进度但规则统计为0")
    
    # 问题4: AI思考过程异常
    ai_thinking = state.get('ai_thinking', '')
    if ai_thinking and len(ai_thinking) > 10000:
        if ai_thinking.count('## 🔍') > 1:
            issues.append("🔴 AI思考异常: 包含多个阶段但状态未更新")
    
    if issues:
        for issue in issues:
            print(issue)
    else:
        print("✅ 未发现明显问题")

def suggest_solutions():
    """建议解决方案"""
    print("\n" + "=" * 80)
    print("💡 建议解决方案")
    print("=" * 80)
    
    solutions = [
        "1. 检查后端进程是否正常运行",
        "2. 查看后端日志是否有错误信息",
        "3. 检查LLM API连接是否正常",
        "4. 重启审核进程",
        "5. 检查数据文件是否完整",
        "6. 使用调试快捷键强制刷新状态 (Ctrl+Shift+R)",
        "7. 手动触发调试解析 (Ctrl+Shift+D)"
    ]
    
    for solution in solutions:
        print(solution)

def monitor_state_changes(interval=5):
    """监控状态变化"""
    print(f"\n🔄 开始监控状态变化 (每{interval}秒检查一次)")
    print("按 Ctrl+C 停止监控")
    print("-" * 80)
    
    last_state = None
    
    try:
        while True:
            current_state = read_audit_state()
            
            if current_state != last_state:
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"\n[{timestamp}] 状态发生变化:")
                
                if current_state:
                    print(f"  状态: {current_state.get('audit_status', 'N/A')}")
                    print(f"  阶段: {current_state.get('current_phase', 'N/A')}")
                    print(f"  进度: {current_state.get('progress_percent', 0)}%")
                    print(f"  消息: {current_state.get('message', 'N/A')}")
                else:
                    print("  状态文件不存在或读取失败")
                
                last_state = current_state
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 监控已停止")

def main():
    """主函数"""
    print("🔧 审核状态调试工具")
    print("=" * 80)
    
    while True:
        print("\n请选择操作:")
        print("1. 分析当前状态")
        print("2. 检查潜在问题")
        print("3. 查看解决方案")
        print("4. 监控状态变化")
        print("5. 退出")
        
        choice = input("\n请输入选择 (1-5): ").strip()
        
        if choice == '1':
            state = read_audit_state()
            analyze_audit_state(state)
            
        elif choice == '2':
            state = read_audit_state()
            check_potential_issues(state)
            
        elif choice == '3':
            suggest_solutions()
            
        elif choice == '4':
            try:
                interval = int(input("请输入监控间隔(秒，默认5): ") or "5")
                monitor_state_changes(interval)
            except ValueError:
                print("❌ 无效的间隔时间")
                
        elif choice == '5':
            print("👋 再见!")
            break
            
        else:
            print("❌ 无效选择，请重试")

if __name__ == "__main__":
    main()
