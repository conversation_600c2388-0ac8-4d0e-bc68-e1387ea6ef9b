<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI审核结果 - 智能分析报告</title>
    <link rel="stylesheet" href="ai_results.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Rajdhani:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- 动态背景 -->
    <div class="cosmic-background" id="cosmic-background"></div>
    
    <!-- 主容器 -->
    <div class="results-container">
        <!-- 顶部标题区域 -->
        <header class="results-header">
            <div class="header-glow"></div>
            <div class="ai-emblem">
                <div class="emblem-core"></div>
                <div class="emblem-rings">
                    <div class="ring ring-1"></div>
                    <div class="ring ring-2"></div>
                    <div class="ring ring-3"></div>
                </div>
            </div>
            <h1 class="main-title">AI智能审核报告</h1>
            <p class="subtitle">基于神经网络的深度分析结果</p>
            <div class="scan-line"></div>
        </header>





        <!-- 统计数据展示 -->
        <section class="stats-dashboard">
            <div class="dashboard-title">
                <h3>智能审核结果及执行建议</h3>
                <div class="title-line"></div>
            </div>
            <div class="stats-grid">
                <div class="stat-card total-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">🔍</div>
                    <div class="card-content">
                        <div class="card-number" id="total-count">--</div>
                        <div class="card-label">智能审核要点</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>
                
                <div class="stat-card passed-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">✅</div>
                    <div class="card-content">
                        <div class="card-number" id="passed-count">--</div>
                        <div class="card-label">通过项目</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>
                
                <div class="stat-card warning-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">⚠️</div>
                    <div class="card-content">
                        <div class="card-number" id="warning-count">--</div>
                        <div class="card-label">警告项目</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>

                <div class="stat-card unknown-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">❓</div>
                    <div class="card-content">
                        <div class="card-number" id="unknown-count">--</div>
                        <div class="card-label">无法判断</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>

                <div class="stat-card failed-rules">
                    <div class="card-glow"></div>
                    <div class="card-icon">❌</div>
                    <div class="card-content">
                        <div class="card-number" id="failed-count">--</div>
                        <div class="card-label">失败项目</div>
                    </div>
                    <div class="card-circuit"></div>
                </div>
            </div>

            <!-- 执行建议区域 -->
            <div class="execution-suggestions">
                <div class="suggestions-header">
                    <h4>执行建议</h4>
                </div>
                <div class="suggestions-content" id="suggestions-content">
                    <div class="suggestion-loading">
                        <div class="loading-brain">
                            <div class="brain-wave"></div>
                            <div class="brain-wave"></div>
                            <div class="brain-wave"></div>
                        </div>
                        <p>AI正在生成执行建议...</p>
                    </div>
                </div>
            </div>
        </section>


        <!-- 详细结果表格 -->
        <section class="detailed-results">
            <div class="results-header-section">
                <h3>📋 详细审核结果</h3>
            </div>
            
            <div class="results-table-container">
                <table class="results-table" id="results-table">
                    <thead>
                        <tr>
                            <th>状态</th>
                            <th>审核要点ID</th>
                            <th>审核结果</th>
                            <th>风险等级</th>
                        </tr>
                    </thead>
                    <tbody id="results-tbody">
                        <tr>
                            <td colspan="4" class="loading-row">
                                <div class="table-loading">
                                    <div class="loading-spinner"></div>
                                    <span>正在加载审核数据...</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </section>

        <!-- AI思考过程展示区域 -->
        <section class="ai-thinking-chain" id="ai-thinking-chain">
            <div class="thinking-header">
                <h3>🤖 AI思考过程</h3>
                <div class="thinking-controls">
                    <button class="control-btn" id="expand-all-btn">
                        <span class="btn-icon">📖</span>
                        <span class="btn-text">展开全部</span>
                    </button>
                    <button class="control-btn" id="collapse-all-btn">
                        <span class="btn-icon">📚</span>
                        <span class="btn-text">收起全部</span>
                    </button>
                    <button class="control-btn" id="copy-thinking-btn">
                        <span class="btn-icon">📋</span>
                        <span class="btn-text">复制内容</span>
                    </button>
                </div>
            </div>

            <div class="thinking-search">
                <div class="search-container">
                    <input type="text" id="thinking-search-input" placeholder="搜索AI思考内容..." class="search-input">
                    <button class="search-btn" id="thinking-search-btn">🔍</button>
                </div>
                <div class="search-results" id="search-results"></div>
            </div>

            <div class="thinking-content" id="thinking-content">
                <div class="thinking-loading">
                    <div class="loading-spinner"></div>
                    <p>正在加载AI思考过程...</p>
                </div>
            </div>

            <div class="thinking-metadata" id="thinking-metadata" style="display: none;">
                <div class="metadata-title">📊 思维链元数据</div>
                <div class="metadata-content" id="metadata-content"></div>
            </div>
        </section>

        <!-- 页脚信息 -->
        <footer class="results-footer">
            <div class="footer-content">
                <div class="ai-signature">
                    <div class="signature-icon">🤖</div>
                    <div class="signature-text">
                        <span class="signature-title">AI财务审核系统</span>
                        <span class="signature-version">Neural Engine v2.0</span>
                    </div>
                </div>
                <div class="generation-info">
                    <span>报告生成时间: <span id="generation-time">--</span></span>
                    <span>分析耗时: <span id="analysis-duration">--</span></span>
                </div>
            </div>
        </footer>
    </div>

    <!-- 成功提示 -->
    <div class="success-toast" id="success-toast" style="display: none;">
        <div class="toast-content">
            <div class="toast-icon">✅</div>
            <div class="toast-message">操作成功完成！</div>
        </div>
    </div>

    <script src="ai_results.js"></script>
</body>
</html>
