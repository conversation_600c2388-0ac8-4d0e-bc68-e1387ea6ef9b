// AI审核结果页面JavaScript逻辑
class AIResultsViewer {
    constructor() {
        this.auditData = null;
        this.currentFilter = 'all';
        this.animationDelay = 100;

        // 动态更新相关属性
        this.isDynamicMode = false;
        this.dynamicUpdateInterval = null;
        this.lastCombinedThinking = '';
        this.lastFileModifiedTime = '';
        this.documentNumber = null;
        this.updateCheckInterval = 2000; // 2秒检查一次

        this.init();
    }
    
    init() {
        this.updateTime();
        this.setupEventListeners();
        this.checkDynamicMode();
        this.loadAuditResults();
        this.startCosmicAnimation();

        // 每秒更新时间
        setInterval(() => this.updateTime(), 1000);
    }

    checkDynamicMode() {
        // 检查URL参数是否启用动态模式
        const urlParams = new URLSearchParams(window.location.search);
        this.isDynamicMode = urlParams.get('dynamic') === 'true';
        this.documentNumber = urlParams.get('doc') || 'ZDBXD2025042900003';

        if (this.isDynamicMode) {
            console.log('🔄 启用动态模式，文档编号:', this.documentNumber);
            this.addDynamicModeIndicator();
        } else {
            console.log('📄 使用静态模式');
        }
    }

    addDynamicModeIndicator() {
        // 在页面顶部添加动态模式指示器
        const indicator = document.createElement('div');
        indicator.id = 'dynamic-mode-indicator';
        indicator.innerHTML = `
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                        color: white; padding: 8px 16px; text-align: center;
                        font-size: 14px; font-weight: 500; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                🔄 实时模式已启用 - 思维链内容将自动更新
                <span id="last-update-time" style="margin-left: 10px; opacity: 0.8;"></span>
                <button id="toggle-dynamic-btn" style="margin-left: 15px; background: rgba(255,255,255,0.2);
                        border: 1px solid rgba(255,255,255,0.3); color: white; padding: 4px 8px;
                        border-radius: 4px; cursor: pointer; font-size: 12px;">
                    暂停更新
                </button>
            </div>
        `;

        document.body.insertBefore(indicator, document.body.firstChild);

        // 添加切换按钮事件
        document.getElementById('toggle-dynamic-btn').addEventListener('click', () => {
            this.toggleDynamicMode();
        });
    }
    
    updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        document.getElementById('generation-time').textContent = timeString;
        
        // 模拟分析耗时
        const duration = '2.3秒';
        document.getElementById('analysis-duration').textContent = duration;
    }
    
    setupEventListeners() {
        // 筛选按钮
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.setFilter(e.target.dataset.filter);
            });
        });

        // AI思维链相关事件监听器
        this.setupThinkingChainListeners();
    }

    setupThinkingChainListeners() {
        // 展开全部按钮
        document.getElementById('expand-all-btn').addEventListener('click', () => {
            this.expandAllPhases();
        });

        // 收起全部按钮
        document.getElementById('collapse-all-btn').addEventListener('click', () => {
            this.collapseAllPhases();
        });

        // 复制内容按钮
        document.getElementById('copy-thinking-btn').addEventListener('click', () => {
            this.copyThinkingContent();
        });

        // 搜索功能
        document.getElementById('thinking-search-btn').addEventListener('click', () => {
            this.searchThinkingContent();
        });

        document.getElementById('thinking-search-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.searchThinkingContent();
            }
        });

        // 实时搜索
        document.getElementById('thinking-search-input').addEventListener('input', (e) => {
            if (e.target.value.length > 2) {
                this.searchThinkingContent();
            } else {
                this.clearSearchResults();
            }
        });
    }
    
    startCosmicAnimation() {
        // 添加动态星星效果
        const cosmicBg = document.getElementById('cosmic-background');
        
        setInterval(() => {
            this.createShootingStar(cosmicBg);
        }, 3000);
    }
    
    createShootingStar(container) {
        const star = document.createElement('div');
        star.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: linear-gradient(45deg, #00d4ff, #ffffff);
            border-radius: 50%;
            top: ${Math.random() * 50}%;
            left: -10px;
            box-shadow: 0 0 10px #00d4ff;
            animation: shootingStar 2s linear forwards;
        `;
        
        container.appendChild(star);
        
        // 添加流星尾迹
        const trail = document.createElement('div');
        trail.style.cssText = `
            position: absolute;
            width: 50px;
            height: 1px;
            background: linear-gradient(90deg, transparent, #00d4ff, transparent);
            top: ${star.style.top};
            left: -60px;
            animation: shootingTrail 2s linear forwards;
        `;
        
        container.appendChild(trail);
        
        // 清理元素
        setTimeout(() => {
            star.remove();
            trail.remove();
        }, 2000);
        
        // 添加流星动画CSS
        if (!document.getElementById('shooting-star-styles')) {
            const style = document.createElement('style');
            style.id = 'shooting-star-styles';
            style.textContent = `
                @keyframes shootingStar {
                    0% { transform: translateX(0) translateY(0); opacity: 0; }
                    10% { opacity: 1; }
                    90% { opacity: 1; }
                    100% { transform: translateX(100vw) translateY(50px); opacity: 0; }
                }
                @keyframes shootingTrail {
                    0% { transform: translateX(0) translateY(0); opacity: 0; }
                    10% { opacity: 0.8; }
                    90% { opacity: 0.8; }
                    100% { transform: translateX(100vw) translateY(50px); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
    }
    
    async loadAuditResults() {
        try {
            // 显示加载状态
            this.showLoadingState();

            // 模拟加载延迟，增加仪式感
            await this.sleep(2000);

            // 获取URL参数中的单据编号
            const urlParams = new URLSearchParams(window.location.search);
            const documentNumber = urlParams.get('doc') || urlParams.get('document') ||
                                  urlParams.get('number') || urlParams.get('doc-num');

            // 如果有单据编号参数，优先尝试对应的文件
            if (documentNumber) {
                console.log(`🔍 检测到单据编号参数: ${documentNumber}`);
                const specificPath = `../audit_reports/audit_report_${documentNumber}.json`;

                try {
                    console.log(`🔄 尝试加载特定单据报告: ${specificPath}`);
                    const response = await fetch(specificPath);
                    if (response.ok) {
                        console.log(`✅ 成功加载单据报告: ${specificPath}`);
                        this.auditData = await response.json();
                        this.displayResults();
                        return;
                    } else {
                        // 特定单据文件不存在，显示未审核提示
                        console.log(`❌ 单据 ${documentNumber} 的审核报告不存在`);
                        this.showNotAuditedState(documentNumber);
                        return;
                    }
                } catch (error) {
                    // 特定单据文件加载失败，显示未审核提示
                    console.log(`❌ 单据 ${documentNumber} 的审核报告加载失败:`, error);
                    this.showNotAuditedState(documentNumber);
                    return;
                }
            }

            // 没有单据编号参数，尝试加载默认的审核报告文件
            console.log('⚠️ 未提供单据编号参数，尝试加载默认报告文件');
            await this.loadDefaultAuditReport();

        } catch (error) {
            console.log('❌ 加载过程发生错误:', error);
            this.showErrorState(error.message);
        }
    }

    async loadDefaultAuditReport() {
        // 尝试加载可能存在的审核报告文件
        const possibleFiles = [
            '../audit_reports/audit_report_123.json',
            '../audit_reports/audit_report_ZDBXD2025042900003.json',
            '../audit_reports/enhanced_audit_report_ZDBXD2025042900003.json',
            '../audit_reports/demo_enhanced_report.json'
        ];

        for (const filePath of possibleFiles) {
            try {
                console.log(`🔄 尝试加载默认报告: ${filePath}`);
                const response = await fetch(filePath);
                if (response.ok) {
                    console.log(`✅ 成功加载默认报告: ${filePath}`);
                    this.auditData = await response.json();
                    this.displayResults();
                    return;
                }
            } catch (error) {
                console.log(`❌ 加载 ${filePath} 失败:`, error);
                continue;
            }
        }

        // 如果所有文件都加载失败，显示提示信息
        console.log('❌ 未找到可用的审核报告文件');
        this.showNoDocumentNumberState();
    }
    
    showLoadingState() {
        // 状态显示已移除，保留方法以避免调用错误
        console.log('🔄 开始加载审核数据...');
    }

    showNotAuditedState(documentNumber) {
        console.log(`⚠️ 单据 "${documentNumber}" 尚未进行AI智能审核`);

        // 隐藏统计数据和详细结果
        this.hideResultsContent();

        // 显示提示信息
        this.showNotAuditedMessage(documentNumber);
    }

    showNoDocumentNumberState() {
        console.log('❌ 缺少单据编号参数');

        // 隐藏统计数据和详细结果
        this.hideResultsContent();

        // 显示使用说明
        this.showUsageInstructions();
    }

    showErrorState(errorMessage) {
        console.log(`❌ 系统错误：${errorMessage}`);

        // 隐藏统计数据和详细结果
        this.hideResultsContent();
    }
    
    generateMockData() {
        return {
            summary: {
                total_rules_checked: 38,
                passed_count: 35,
                failed_count: 1,
                warning_count: 2
            },
            details: [
                { rule_id: '规则1：检查发票上传', status: 'PASS', message: '发票已正确上传并验证通过' },
                { rule_id: '规则7：检查招待对象', status: 'WARNING', message: '招待对象中包含敏感关键词，建议人工复核' },
                { rule_id: '规则15：语义一致性分析', status: 'FAIL', message: '申报事由与实际消费内容存在逻辑冲突' },
                { rule_id: '规则20：高档场所检测', status: 'WARNING', message: '检测到高档消费场所，需要额外审批' }
            ],
            review_comments: '经AI智能分析，发现2个警告项和1个失败项，建议人工进一步审核。'
        };
    }
    
    async displayResults() {
        const summary = this.auditData.summary;
        
        // 延迟显示统计数据，增加动画效果
        await this.sleep(500);
        this.updateStatistics(summary);
        
        // 延迟显示执行建议
        await this.sleep(800);
        this.updateExecutionSuggestions();
        
        // 延迟显示详细结果
        await this.sleep(1000);
        this.updateDetailedResults();

        // 自动加载AI思维链内容
        await this.sleep(1200);
        console.log('🚀 开始自动加载AI思维链内容...');
        this.autoLoadThinkingChain();

        // 如果启用动态模式，开始轮询更新
        if (this.isDynamicMode) {
            this.startDynamicUpdates();
        }
    }
    

    
    async updateStatistics(summary) {
        // 从详细数据中动态计算各状态数量，确保准确性
        const details = this.auditData.details || [];
        const statusCounts = {
            PASS: 0,
            WARNING: 0,
            UNKNOWN: 0,
            FAIL: 0
        };

        details.forEach(item => {
            if (statusCounts.hasOwnProperty(item.status)) {
                statusCounts[item.status]++;
            }
        });

        const stats = [
            { id: 'total-count', value: summary.total_rules_checked },
            { id: 'passed-count', value: statusCounts.PASS },
            { id: 'warning-count', value: statusCounts.WARNING },
            { id: 'unknown-count', value: statusCounts.UNKNOWN },
            { id: 'failed-count', value: statusCounts.FAIL }
        ];
        
        // 逐个显示统计数据，增加动画效果
        for (let i = 0; i < stats.length; i++) {
            await this.sleep(200);
            this.animateCounter(stats[i].id, stats[i].value);
        }
    }
    
    animateCounter(elementId, targetValue) {
        const element = document.getElementById(elementId);
        const duration = 1000;
        const steps = 30;
        const increment = targetValue / steps;
        let current = 0;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= targetValue) {
                current = targetValue;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current);
        }, duration / steps);
    }
    
    async updateExecutionSuggestions() {
        const suggestionsContent = document.getElementById('suggestions-content');

        // 生成结构化的执行建议数据
        const suggestionData = this.generateExecutionSuggestions();

        suggestionsContent.innerHTML = `
            <div class="suggestion-cards-container">
                <!-- 审核结论卡片 -->
                <div class="suggestion-card conclusion-card ${suggestionData.riskLevel}" data-risk="${suggestionData.riskLevel}">
                    <div class="card-header">
                        <div class="card-icon">${suggestionData.conclusion.icon}</div>
                        <div class="card-title">
                            <h4>审核结论</h4>
                            <span class="risk-badge ${suggestionData.riskLevel}">${suggestionData.conclusion.riskText}</span>
                        </div>
                    </div>
                    <div class="card-content">
                        <div class="conclusion-content">
                            <div class="conclusion-text">
                                <p>${suggestionData.conclusion.summary}</p>
                            </div>
                            <div class="stats-summary">
                                <div class="stat-item">
                                    <span class="stat-label">检查要点</span>
                                    <span class="stat-value">${suggestionData.conclusion.totalRules} 项</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-label">通过要点</span>
                                    <span class="stat-value">${suggestionData.conclusion.passedRules} 项</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 问题详情卡片 -->
                ${suggestionData.issues.length > 0 ? `
                <div class="suggestion-card issues-card">
                    <div class="card-header">
                        <div class="card-icon">🔍</div>
                        <div class="card-title">
                            <h4>发现的问题</h4>
                            <span class="issue-count">${suggestionData.issues.length} 项</span>
                        </div>
                        <button class="expand-btn" onclick="toggleIssueDetails(this)">
                            <span class="expand-text">展开详情</span>
                            <span class="expand-icon">▼</span>
                        </button>
                    </div>
                    <div class="card-content">
                        <div class="issues-summary">
                            ${suggestionData.issues.map(issue => `
                                <div class="issue-summary-item ${issue.type}">
                                    <span class="issue-icon">${issue.icon}</span>
                                    <span class="issue-text">${issue.count} 项${issue.label}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="issues-details" style="display: none;">
                            ${suggestionData.issues.map(issue =>
                                issue.rules.length > 0 ? `
                                <div class="issue-category ${issue.type}">
                                    <h5>${issue.icon} ${issue.label}</h5>
                                    ${issue.rules.map((rule, index) => `
                                        <div class="issue-item">
                                            <div class="issue-number">${index + 1}</div>
                                            <div class="issue-content">
                                                <div class="issue-rule">${rule.rule_id}</div>
                                                <div class="issue-reason">${rule.reason}</div>
                                                <div class="issue-impact">影响等级：${rule.impact}</div>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                                ` : ''
                            ).join('')}
                        </div>
                    </div>
                </div>
                ` : ''}

            </div>
        `;

        // 添加交互功能
        this.addSuggestionInteractions();

        // 添加样式
        this.addSuggestionStyles();
    }

    addSuggestionInteractions() {
        // 添加全局函数到window对象，供HTML调用
        window.toggleIssueDetails = (button) => {
            const card = button.closest('.suggestion-card');
            const details = card.querySelector('.issues-details');
            const expandIcon = button.querySelector('.expand-icon');
            const expandText = button.querySelector('.expand-text');

            if (details.style.display === 'none') {
                details.style.display = 'block';
                expandIcon.textContent = '▲';
                expandText.textContent = '收起详情';
                button.classList.add('expanded');
            } else {
                details.style.display = 'none';
                expandIcon.textContent = '▼';
                expandText.textContent = '展开详情';
                button.classList.remove('expanded');
            }
        };

    }

    addSuggestionStyles() {
        if (document.getElementById('suggestion-cards-styles')) {
            return; // 样式已存在
        }

        const style = document.createElement('style');
        style.id = 'suggestion-cards-styles';
        style.textContent = `
            /* 建议卡片容器 */
            .suggestion-cards-container {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            /* 基础卡片样式 */
            .suggestion-card {
                background: rgba(0, 0, 0, 0.4);
                border-radius: 15px;
                border: 1px solid rgba(255, 255, 255, 0.1);
                overflow: hidden;
                transition: all 0.3s ease;
            }

            .suggestion-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            }

            /* 卡片头部 */
            .card-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 20px 25px;
                background: rgba(255, 255, 255, 0.05);
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }

            .card-header .card-icon {
                font-size: 1.5rem;
                margin-right: 12px;
            }

            .card-title {
                display: flex;
                align-items: center;
                gap: 12px;
                flex: 1;
            }

            .card-title h4 {
                color: var(--text-primary);
                margin: 0;
                font-size: 1.1rem;
                font-weight: 600;
            }

            /* 风险等级徽章 */
            .risk-badge {
                padding: 4px 12px;
                border-radius: 20px;
                font-size: 0.8rem;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .risk-badge.high-risk {
                background: rgba(255, 59, 48, 0.2);
                color: #ff3b30;
                border: 1px solid rgba(255, 59, 48, 0.3);
            }

            .risk-badge.medium-risk {
                background: rgba(255, 149, 0, 0.2);
                color: #ff9500;
                border: 1px solid rgba(255, 149, 0, 0.3);
            }

            .risk-badge.unknown-risk {
                background: rgba(139, 69, 255, 0.2);
                color: #8b45ff;
                border: 1px solid rgba(139, 69, 255, 0.3);
            }

            .risk-badge.low-risk {
                background: rgba(52, 199, 89, 0.2);
                color: #34c759;
                border: 1px solid rgba(52, 199, 89, 0.3);
            }

            /* 卡片内容 */
            .card-content {
                padding: 25px;
            }

            /* 审核结论卡片特殊样式 */
            .conclusion-card.high-risk {
                border-left: 4px solid #ff3b30;
            }

            .conclusion-card.medium-risk {
                border-left: 4px solid #ff9500;
            }

            .conclusion-card.unknown-risk {
                border-left: 4px solid #8b45ff;
            }

            .conclusion-card.low-risk {
                border-left: 4px solid #34c759;
            }

            /* 审核结论内容样式 */
            .conclusion-content {
                display: flex;
                flex-direction: column;
                gap: 20px;
            }

            .conclusion-text {
                flex: 1;
            }

            .conclusion-text p {
                color: var(--text-secondary);
                line-height: 1.6;
                margin: 0;
                font-size: 0.95rem;
            }

            .stats-summary {
                display: flex;
                gap: 20px;
                flex-wrap: wrap;
            }

            .stat-item {
                display: flex;
                flex-direction: column;
                gap: 4px;
                padding: 12px 16px;
                background: rgba(255, 255, 255, 0.05);
                border-radius: 8px;
                border-left: 3px solid var(--accent-blue);
                min-width: 120px;
            }

            .stat-label {
                font-size: 0.8rem;
                color: var(--text-tertiary);
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
            }

            .stat-value {
                font-size: 1.1rem;
                color: var(--text-primary);
                font-weight: 600;
            }

            /* 展开按钮 */
            .expand-btn {
                display: flex;
                align-items: center;
                gap: 8px;
                background: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 8px;
                padding: 8px 12px;
                color: var(--text-secondary);
                font-size: 0.85rem;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .expand-btn:hover {
                background: rgba(255, 255, 255, 0.15);
                color: var(--text-primary);
            }

            .expand-btn.expanded {
                background: rgba(139, 69, 255, 0.2);
                border-color: rgba(139, 69, 255, 0.4);
                color: var(--accent-purple);
            }

            .expand-icon {
                transition: transform 0.3s ease;
            }

            /* 问题计数 */
            .issue-count {
                background: rgba(255, 149, 0, 0.2);
                color: #ff9500;
                padding: 2px 8px;
                border-radius: 12px;
                font-size: 0.8rem;
                font-weight: 600;
            }

            /* 问题摘要 */
            .issues-summary {
                display: flex;
                flex-wrap: wrap;
                gap: 15px;
                margin-bottom: 20px;
            }

            .issue-summary-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 12px;
                border-radius: 8px;
                font-size: 0.9rem;
                font-weight: 500;
            }

            .issue-summary-item.failed {
                background: rgba(255, 59, 48, 0.1);
                color: #ff3b30;
                border: 1px solid rgba(255, 59, 48, 0.2);
            }

            .issue-summary-item.warning {
                background: rgba(255, 149, 0, 0.1);
                color: #ff9500;
                border: 1px solid rgba(255, 149, 0, 0.2);
            }

            .issue-summary-item.unknown {
                background: rgba(139, 69, 255, 0.1);
                color: #8b45ff;
                border: 1px solid rgba(139, 69, 255, 0.2);
            }

            /* 问题详情 */
            .issues-details {
                animation: slideDown 0.3s ease;
            }

            @keyframes slideDown {
                from {
                    opacity: 0;
                    max-height: 0;
                }
                to {
                    opacity: 1;
                    max-height: 1000px;
                }
            }

            .issue-category {
                margin-bottom: 25px;
            }

            .issue-category h5 {
                color: var(--text-primary);
                margin: 0 0 15px 0;
                font-size: 1rem;
                font-weight: 600;
                display: flex;
                align-items: center;
                gap: 8px;
            }

            .issue-item {
                display: flex;
                gap: 15px;
                margin-bottom: 15px;
                padding: 15px;
                background: rgba(0, 0, 0, 0.3);
                border-radius: 10px;
                border-left: 3px solid rgba(255, 255, 255, 0.2);
            }

            .issue-category.failed .issue-item {
                border-left-color: #ff3b30;
            }

            .issue-category.warning .issue-item {
                border-left-color: #ff9500;
            }

            .issue-category.unknown .issue-item {
                border-left-color: #8b45ff;
            }

            .issue-number {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 24px;
                height: 24px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                color: var(--text-secondary);
                font-size: 0.8rem;
                font-weight: 600;
                flex-shrink: 0;
            }

            .issue-content {
                flex: 1;
            }

            .issue-rule {
                color: var(--text-primary);
                font-weight: 600;
                margin-bottom: 8px;
                font-size: 0.95rem;
            }

            .issue-reason {
                color: var(--text-secondary);
                line-height: 1.5;
                margin-bottom: 8px;
                font-size: 0.9rem;
            }

            .issue-impact {
                color: var(--text-tertiary);
                font-size: 0.8rem;
                font-style: italic;
            }

            /* 响应式设计 */
            @media (max-width: 768px) {
                .suggestion-cards-container {
                    gap: 15px;
                }

                .card-header {
                    padding: 15px 20px;
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 10px;
                }

                .card-content {
                    padding: 20px;
                }

                .conclusion-content {
                    gap: 15px;
                }

                .stats-summary {
                    flex-direction: column;
                    gap: 12px;
                }

                .stat-item {
                    min-width: auto;
                }

                .issues-summary {
                    flex-direction: column;
                    gap: 10px;
                }
            }

            @media (max-width: 480px) {
                .card-header {
                    padding: 12px 15px;
                }

                .card-content {
                    padding: 15px;
                }

                .issue-item {
                    flex-direction: column;
                    gap: 10px;
                }

                .issue-number {
                    align-self: flex-start;
                }
            }
        `;

        document.head.appendChild(style);
    }
    
    generateExecutionSuggestions() {
        const summary = this.auditData.summary;
        const details = this.auditData.details || [];

        // 动态计算各状态数量
        const statusCounts = {
            PASS: 0,
            WARNING: 0,
            UNKNOWN: 0,
            FAIL: 0
        };

        // 分类收集各状态的规则
        const failedRules = [];
        const warningRules = [];
        const unknownRules = [];

        details.forEach(item => {
            if (statusCounts.hasOwnProperty(item.status)) {
                statusCounts[item.status]++;

                if (item.status === 'FAIL') {
                    failedRules.push({...item, impact: '严重 - 必须解决后方可审批'});
                } else if (item.status === 'WARNING') {
                    warningRules.push({...item, impact: '中等 - 建议核实确认'});
                } else if (item.status === 'UNKNOWN') {
                    unknownRules.push({...item, impact: '不确定 - 需要人工判断'});
                }
            }
        });

        const totalRules = summary.total_rules_checked;
        const passedRules = statusCounts.PASS;

        // 确定风险等级和主题
        let riskLevel, conclusion, actions;

        // 计算总问题数量
        const totalIssues = statusCounts.FAIL + statusCounts.WARNING + statusCounts.UNKNOWN;

        if (statusCounts.FAIL > 0) {
            riskLevel = 'high-risk';
            conclusion = {
                icon: '🚨',
                riskText: '高风险',
                summary: `发现 ${statusCounts.FAIL} 项严重违规问题，存在重大合规风险，不符合审批条件`,
                totalRules: totalRules,
                passedRules: passedRules
            };
            actions = [
                '立即通知申请人补充或修正相关材料',
                '要求申请人重新提交完整的审批文件',
                '建议财务部门暂停该项目的所有相关支付',
                '需要部门负责人确认问题解决后才能重新审核'
            ];
        } else if (statusCounts.WARNING > 0 && statusCounts.UNKNOWN > 0) {
            // 同时存在WARNING和UNKNOWN的情况
            riskLevel = 'medium-risk';
            conclusion = {
                icon: '⚠️',
                riskText: '中等风险',
                summary: `发现 ${totalIssues} 项需要关注的问题（${statusCounts.WARNING} 项警告，${statusCounts.UNKNOWN} 项无法判断），建议在解决相关问题后再进行审批`,
                totalRules: totalRules,
                passedRules: passedRules
            };
            actions = [
                '联系申请人核实相关问题并提供说明',
                '安排相关业务人员对无法判断项目进行人工复核',
                '建议财务部门进行二次人工审核',
                '可考虑设置条件性审批（问题解决后生效）',
                '建议加强后续监控和定期复查'
            ];
        } else if (statusCounts.WARNING > 0) {
            // 只有WARNING的情况
            riskLevel = 'medium-risk';
            conclusion = {
                icon: '⚠️',
                riskText: '中等风险',
                summary: `发现 ${statusCounts.WARNING} 项需要关注的问题，建议在解决相关问题后再进行审批`,
                totalRules: totalRules,
                passedRules: passedRules
            };
            actions = [
                '联系申请人核实相关问题并提供说明',
                '建议财务部门进行二次人工审核',
                '可考虑设置条件性审批（问题解决后生效）',
                '建议加强后续监控和定期复查'
            ];
        } else if (statusCounts.UNKNOWN > 0) {
            // 只有UNKNOWN的情况
            riskLevel = 'unknown-risk';
            conclusion = {
                icon: '❓',
                riskText: '不确定',
                summary: `有 ${statusCounts.UNKNOWN} 项无法自动判断的规则，建议人工审核确认后再做决定`,
                totalRules: totalRules,
                passedRules: passedRules
            };
            actions = [
                '安排相关业务人员进行人工复核',
                '补充完善相关数据字段信息',
                '建议优化审核规则以减少不确定项',
                '可在人工确认后继续审批流程'
            ];
        } else {
            // 全部通过的情况
            riskLevel = 'low-risk';
            conclusion = {
                icon: '✅',
                riskText: '低风险',
                summary: `全部通过 ${passedRules} 项规则检查，符合审批条件，可以正常进入审批流程`,
                totalRules: totalRules,
                passedRules: passedRules
            };
            actions = [
                '可以正常进入下一步审批流程',
                '建议加快处理速度，提高审批效率',
                '继续保持良好的合规管理水平',
                '定期回顾和优化审核流程'
            ];
        }

        // 构建问题数组
        const issues = [];

        if (failedRules.length > 0) {
            issues.push({
                type: 'failed',
                icon: '🔴',
                label: '严重违规问题',
                count: failedRules.length,
                rules: failedRules
            });
        }

        if (warningRules.length > 0) {
            issues.push({
                type: 'warning',
                icon: '🟡',
                label: '需要关注的问题',
                count: warningRules.length,
                rules: warningRules
            });
        }

        if (unknownRules.length > 0) {
            issues.push({
                type: 'unknown',
                icon: '🔵',
                label: '无法自动判断的项目',
                count: unknownRules.length,
                rules: unknownRules
            });
        }

        // 返回结构化数据
        return {
            riskLevel: riskLevel,
            conclusion: conclusion,
            issues: issues,
            actions: actions
        };
    }
    
    updateDetailedResults() {
        const tbody = document.getElementById('results-tbody');
        const details = this.auditData.details;

        tbody.innerHTML = '';

        // 按状态优先级排序：FAIL > WARNING > UNKNOWN > PASS
        const sortedDetails = this.sortDetailsByPriority(details);

        sortedDetails.forEach((item, index) => {
            setTimeout(() => {
                const row = this.createResultRow(item);
                tbody.appendChild(row);

                // 添加进入动画
                row.style.opacity = '0';
                row.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    row.style.transition = 'all 0.5s ease';
                    row.style.opacity = '1';
                    row.style.transform = 'translateY(0)';
                }, 50);
            }, index * 100);
        });
    }

    sortDetailsByPriority(details) {
        // 定义状态优先级：数字越小优先级越高
        const statusPriority = {
            'FAIL': 1,      // 失败项目 - 最高优先级
            'WARNING': 2,   // 警告项目
            'UNKNOWN': 3,   // 无法执行项目
            'PASS': 4       // 通过项目 - 最低优先级
        };

        return [...details].sort((a, b) => {
            const priorityA = statusPriority[a.status] || 5; // 未知状态放在最后
            const priorityB = statusPriority[b.status] || 5;

            // 如果优先级相同，按原始顺序保持稳定排序
            if (priorityA === priorityB) {
                return 0;
            }

            return priorityA - priorityB;
        });
    }
    
    createResultRow(item) {
        const row = document.createElement('tr');
        row.dataset.status = item.status;



        const statusClass = {
            'PASS': 'pass',
            'WARNING': 'warning',
            'FAIL': 'fail',
            'UNKNOWN': 'unknown'
        }[item.status] || 'unknown';

        const statusText = {
            'PASS': '通过',
            'WARNING': '警告',
            'FAIL': '失败',
            'UNKNOWN': '无法判断'
        }[item.status] || '未知状态';

        const riskLevel = {
            'PASS': 'low',
            'WARNING': 'medium',
            'FAIL': 'high',
            'UNKNOWN': 'medium'
        }[item.status] || 'medium';
        
        const riskText = {
            'low': '低风险',
            'medium': '中风险',
            'high': '高风险'
        }[riskLevel];
        
        const reasonText = item.reason || '无详细信息';
        const truncatedReason = reasonText.length > 100 ? reasonText.substring(0, 100) + '...' : reasonText;

        row.innerHTML = `
            <td>
                <div class="status-badge ${statusClass}">
                    <div class="status-icon"></div>
                    ${statusText}
                </div>
            </td>
            <td>${item.rule_id}</td>
            <td class="reason-cell">
                <div class="reason-content" title="${this.escapeHtml(reasonText)}">
                    <span class="reason-text">${this.escapeHtml(truncatedReason)}</span>
                    ${reasonText.length > 100 ? '<button class="expand-btn" onclick="this.parentElement.parentElement.classList.toggle(\'expanded\')">展开</button>' : ''}
                </div>
                ${reasonText.length > 100 ? `<div class="reason-full">${this.escapeHtml(reasonText)}</div>` : ''}
            </td>
            <td>
                <div class="risk-level ${riskLevel}">${riskText}</div>
            </td>
        `;
        
        return row;
    }
    
    setFilter(filter) {
        this.currentFilter = filter;
        
        // 更新按钮状态
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-filter="${filter}"]`).classList.add('active');
        
        // 过滤表格行
        const rows = document.querySelectorAll('#results-tbody tr');
        rows.forEach(row => {
            if (filter === 'all' || row.dataset.status === filter) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }
    

    
    showSuccessToast(message) {
        const toast = document.getElementById('success-toast');
        const messageEl = document.querySelector('.toast-message');
        
        messageEl.textContent = message;
        toast.style.display = 'block';
        
        setTimeout(() => {
            toast.style.display = 'none';
        }, 3000);
    }
    
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // ==================== 动态更新功能 ====================

    startDynamicUpdates() {
        console.log('🔄 启动动态更新，检查间隔:', this.updateCheckInterval + 'ms');

        this.dynamicUpdateInterval = setInterval(() => {
            this.checkForThinkingUpdates();
        }, this.updateCheckInterval);

        // 立即执行一次检查
        this.checkForThinkingUpdates();
    }

    stopDynamicUpdates() {
        if (this.dynamicUpdateInterval) {
            clearInterval(this.dynamicUpdateInterval);
            this.dynamicUpdateInterval = null;
            console.log('⏸️ 动态更新已停止');
        }
    }

    toggleDynamicMode() {
        const btn = document.getElementById('toggle-dynamic-btn');

        if (this.dynamicUpdateInterval) {
            // 当前正在更新，停止更新
            this.stopDynamicUpdates();
            btn.textContent = '恢复更新';
            btn.style.background = 'rgba(255,255,255,0.3)';
        } else {
            // 当前已停止，恢复更新
            this.startDynamicUpdates();
            btn.textContent = '暂停更新';
            btn.style.background = 'rgba(255,255,255,0.2)';
        }
    }

    async checkForThinkingUpdates() {
        try {
            // 调用新的API端点检查思维链状态
            const response = await fetch(`/api/thinking-status?doc_num=${this.documentNumber}`);

            if (!response.ok) {
                console.log('⚠️ 思维链状态检查失败:', response.status);
                return;
            }

            const statusData = await response.json();

            // 检查是否有错误
            if (statusData.error) {
                console.log('⚠️ 思维链状态API错误:', statusData.message);
                return;
            }

            // 检查文件是否有更新
            const currentModifiedTime = statusData.file_modified_time;
            const currentThinking = statusData.combined_thinking || '';

            if (currentModifiedTime !== this.lastFileModifiedTime ||
                currentThinking !== this.lastCombinedThinking) {

                console.log('🔄 检测到思维链更新');
                console.log('  - 文件修改时间:', currentModifiedTime);
                console.log('  - 内容长度:', statusData.combined_thinking_length);

                // 更新思维链内容
                await this.updateThinkingContent(currentThinking);

                // 更新记录
                this.lastFileModifiedTime = currentModifiedTime;
                this.lastCombinedThinking = currentThinking;

                // 更新指示器
                this.updateDynamicIndicator(currentModifiedTime);
            }

        } catch (error) {
            console.log('❌ 检查思维链更新失败:', error);
        }
    }

    async updateThinkingContent(newThinking) {
        const thinkingContent = document.getElementById('thinking-content');

        if (!thinkingContent || !newThinking) {
            return;
        }

        // 添加更新动画效果
        thinkingContent.style.opacity = '0.7';
        thinkingContent.style.transition = 'opacity 0.3s ease';

        // 等待动画
        await this.sleep(300);

        // 更新内容（复用现有的解析逻辑）
        const combinedContent = newThinking;

        if (combinedContent && combinedContent.length > 50) {
            const combinedHTML = `
                <div class="combined-thinking">
                    <div class="combined-thinking-title">
                        🧠 完整思维链概览 <span class="update-badge">已更新</span>
                    </div>
                    <div class="combined-thinking-content">${this.parseMarkdown(combinedContent)}</div>
                </div>
            `;

            thinkingContent.innerHTML = combinedHTML;

            // 添加更新标记的样式
            const updateBadge = thinkingContent.querySelector('.update-badge');
            if (updateBadge) {
                updateBadge.style.cssText = `
                    background: #4CAF50;
                    color: white;
                    padding: 2px 6px;
                    border-radius: 10px;
                    font-size: 12px;
                    margin-left: 8px;
                    animation: pulse 2s ease-in-out;
                `;

                // 2秒后移除更新标记
                setTimeout(() => {
                    if (updateBadge) {
                        updateBadge.style.display = 'none';
                    }
                }, 2000);
            }
        }

        // 恢复透明度
        thinkingContent.style.opacity = '1';

        console.log('✅ 思维链内容已更新');
    }

    updateDynamicIndicator(lastUpdateTime) {
        const timeElement = document.getElementById('last-update-time');
        if (timeElement) {
            timeElement.textContent = `最后更新: ${lastUpdateTime}`;
        }
    }

    hideResultsContent() {
        // 隐藏统计数据
        const statsSection = document.querySelector('.stats-dashboard');
        if (statsSection) {
            statsSection.style.display = 'none';
        }

        // 隐藏执行建议
        const suggestionsSection = document.querySelector('.execution-suggestions');
        if (suggestionsSection) {
            suggestionsSection.style.display = 'none';
        }

        // 隐藏详细结果
        const resultsSection = document.querySelector('.detailed-results');
        if (resultsSection) {
            resultsSection.style.display = 'none';
        }
    }

    showNotAuditedMessage(documentNumber) {
        // 在执行建议区域显示未审核提示
        const suggestionsContent = document.getElementById('suggestions-content');
        const suggestionsSection = document.querySelector('.execution-suggestions');

        if (suggestionsSection) {
            suggestionsSection.style.display = 'block';
        }

        suggestionsContent.innerHTML = `
            <div class="not-audited-message">
                <div class="message-icon">📋</div>
                <div class="message-content">
                    <h3>单据未审核</h3>
                    <p>单据编号 <strong>"${documentNumber}"</strong> 尚未进行AI智能审核。</p>
                    <div class="next-steps">
                        <h4>下一步操作：</h4>
                        <ul>
                            <li>🔄 前往控制台启动智能审核流程</li>
                            <li>📊 等待审核完成后查看结果</li>
                            <li>📝 确认单据编号是否正确</li>
                        </ul>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="window.open('ai_console.html?doc_num=${documentNumber}', '_blank')">
                            <span class="btn-icon">🚀</span>
                            <span class="btn-text">启动审核</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addNotAuditedStyles();
    }

    showUsageInstructions() {
        // 在执行建议区域显示使用说明
        const suggestionsContent = document.getElementById('suggestions-content');
        const suggestionsSection = document.querySelector('.execution-suggestions');

        if (suggestionsSection) {
            suggestionsSection.style.display = 'block';
        }

        suggestionsContent.innerHTML = `
            <div class="usage-instructions">
                <div class="message-icon">💡</div>
                <div class="message-content">
                    <h3>使用说明</h3>
                    <p>请在URL中提供单据编号参数以查看对应的审核结果。</p>
                    <div class="url-examples">
                        <h4>URL参数示例：</h4>
                        <div class="example-list">
                            <div class="example-item">
                                <code>ai_results.html?doc=12345</code>
                                <span class="example-desc">使用 doc 参数</span>
                            </div>
                            <div class="example-item">
                                <code>ai_results.html?document=DOC001</code>
                                <span class="example-desc">使用 document 参数</span>
                            </div>
                            <div class="example-item">
                                <code>ai_results.html?number=2024-001</code>
                                <span class="example-desc">使用 number 参数</span>
                            </div>
                        </div>
                    </div>
                    <div class="action-buttons">
                        <button class="action-btn primary" onclick="window.open('ai_console.html', '_blank')">
                            <span class="btn-icon">🖥️</span>
                            <span class="btn-text">前往控制台</span>
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addUsageInstructionsStyles();
    }

    addNotAuditedStyles() {
        if (!document.getElementById('not-audited-styles')) {
            const style = document.createElement('style');
            style.id = 'not-audited-styles';
            style.textContent = `
                .not-audited-message {
                    display: flex;
                    align-items: flex-start;
                    gap: 20px;
                    padding: 30px;
                    background: rgba(255, 165, 0, 0.1);
                    border-radius: 15px;
                    border-left: 4px solid var(--accent-orange);
                    margin: 20px 0;
                }
                .not-audited-message .message-icon {
                    font-size: 3rem;
                    margin-top: 10px;
                }
                .not-audited-message .message-content h3 {
                    color: var(--accent-orange);
                    margin-bottom: 15px;
                    font-size: 1.5rem;
                }
                .not-audited-message .message-content p {
                    color: var(--text-secondary);
                    line-height: 1.6;
                    margin-bottom: 20px;
                }
                .next-steps h4 {
                    color: var(--text-primary);
                    margin-bottom: 10px;
                    font-size: 1.1rem;
                }
                .next-steps ul {
                    list-style: none;
                    padding: 0;
                }
                .next-steps li {
                    color: var(--text-secondary);
                    margin-bottom: 8px;
                    padding-left: 10px;
                }
                .action-buttons {
                    margin-top: 25px;
                }
            `;
            document.head.appendChild(style);
        }
    }

    addUsageInstructionsStyles() {
        if (!document.getElementById('usage-instructions-styles')) {
            const style = document.createElement('style');
            style.id = 'usage-instructions-styles';
            style.textContent = `
                .usage-instructions {
                    display: flex;
                    align-items: flex-start;
                    gap: 20px;
                    padding: 30px;
                    background: rgba(255, 0, 0, 0.1);
                    border-radius: 15px;
                    border-left: 4px solid var(--accent-red);
                    margin: 20px 0;
                }
                .usage-instructions .message-icon {
                    font-size: 3rem;
                    margin-top: 10px;
                }
                .usage-instructions .message-content h3 {
                    color: var(--accent-red);
                    margin-bottom: 15px;
                    font-size: 1.5rem;
                }
                .usage-instructions .message-content p {
                    color: var(--text-secondary);
                    line-height: 1.6;
                    margin-bottom: 20px;
                }
                .url-examples h4 {
                    color: var(--text-primary);
                    margin-bottom: 15px;
                    font-size: 1.1rem;
                }
                .example-list {
                    display: flex;
                    flex-direction: column;
                    gap: 10px;
                }
                .example-item {
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    padding: 10px;
                    background: rgba(0, 0, 0, 0.3);
                    border-radius: 8px;
                }
                .example-item code {
                    background: rgba(0, 212, 255, 0.2);
                    color: var(--accent-blue);
                    padding: 5px 10px;
                    border-radius: 4px;
                    font-family: 'Courier New', monospace;
                    font-size: 0.9rem;
                }
                .example-desc {
                    color: var(--text-secondary);
                    font-size: 0.9rem;
                }
                .action-buttons {
                    margin-top: 25px;
                }
            `;
            document.head.appendChild(style);
        }
    }

    // AI思维链相关方法
    autoLoadThinkingChain() {
        console.log('🔍 自动加载AI思维链...');
        console.log('📊 审核数据:', this.auditData);

        // 检查审核数据中是否包含AI思维链
        if (this.auditData && this.auditData.ai_thinking_chain) {
            console.log('✅ 找到AI思维链数据，自动加载内容');
            console.log('🧠 AI思维链内容:', this.auditData.ai_thinking_chain);

            // 自动加载思维链内容
            this.loadThinkingChainContent();

            console.log('🎯 AI思维链已自动加载');
        } else {
            console.log('❌ 未找到AI思维链数据');
            console.log('   - auditData存在:', !!this.auditData);
            console.log('   - ai_thinking_chain存在:', !!(this.auditData && this.auditData.ai_thinking_chain));

            // 隐藏整个思维链区域
            const thinkingChainSection = document.getElementById('ai-thinking-chain');
            thinkingChainSection.style.display = 'none';
        }
    }

    checkAndShowThinkingChainButton() {
        // 保留原方法以兼容其他调用
        this.autoLoadThinkingChain();
    }

    async loadThinkingChainContent() {
        const thinkingContent = document.getElementById('thinking-content');
        const metadataSection = document.getElementById('thinking-metadata');

        try {
            // 显示加载状态
            thinkingContent.innerHTML = `
                <div class="thinking-loading">
                    <div class="loading-spinner"></div>
                    <p>正在加载AI思考过程...</p>
                </div>
            `;

            await this.sleep(1000); // 模拟加载时间

            const aiThinkingData = this.auditData.ai_thinking_chain;

            if (!aiThinkingData) {
                thinkingContent.innerHTML = `
                    <div class="thinking-error">
                        <p>❌ AI思维链数据不可用</p>
                        <p>该审核报告不包含AI思考过程数据</p>
                    </div>
                `;
                return;
            }

            // 构建思维链内容
            let contentHTML = '';

            // 显示组合思维链
            if (aiThinkingData.combined_thinking) {
                // 检查combined_thinking是否缺少第二部分内容
                let combinedContent = aiThinkingData.combined_thinking;

                // 如果combined_thinking不包含第二部分内容，动态补充
                if (!combinedContent.includes('阶段 2/4') && aiThinkingData.phases_history) {
                    const phase2Group1 = aiThinkingData.phases_history['第二部分：字段内容与一致性检查_(第1组)'];
                    const phase2Group2 = aiThinkingData.phases_history['第二部分：字段内容与一致性检查_(第2组)'];

                    if (phase2Group1 || phase2Group2) {
                        // 找到阶段1结束和阶段3开始的位置
                        const stage1End = combinedContent.indexOf('---\n\n## 🔍 金额与标准检查');
                        if (stage1End !== -1) {
                            // 构建第二部分内容
                            let phase2Content = '\n\n---\n\n## 🔍 字段内容与一致性检查 (阶段 2/4)\n\n';

                            if (phase2Group1) {
                                phase2Content += '### 第1组 (规则6-14)\n\n';
                                phase2Content += phase2Group1.ai_thinking + '\n\n';
                            }

                            if (phase2Group2) {
                                phase2Content += '### 第2组 (规则15-24)\n\n';
                                phase2Content += phase2Group2.ai_thinking + '\n\n';
                            }

                            // 插入第二部分内容
                            combinedContent = combinedContent.substring(0, stage1End) +
                                            phase2Content +
                                            combinedContent.substring(stage1End);
                        }
                    }
                }

                contentHTML += `
                    <div class="combined-thinking">
                        <div class="combined-thinking-title">
                            🧠 完整思维链概览
                        </div>
                        <div class="combined-thinking-content">${this.parseMarkdown(combinedContent)}</div>
                    </div>
                `;
            }

            // 显示各阶段思维链
            if (aiThinkingData.phases_history) {
                const phases = Object.entries(aiThinkingData.phases_history);

                if (phases.length > 0) {
                    // 处理和合并阶段数据
                    const processedPhases = [];
                    let phase2Combined = null;



                    phases.forEach(([phaseKey, phaseData]) => {
                        if (phaseKey === 'phase1') {
                            processedPhases.push({
                                key: 'phase1',
                                name: phaseData.phase_name || '附件完整性检查',
                                thinking: phaseData.ai_thinking || '无思考内容',
                                status: phaseData.status || 'completed',
                                timestamp: phaseData.timestamp || '',
                                order: 1
                            });
                        } else if (phaseKey.includes('第二部分')) {
                            if (!phase2Combined) {
                                phase2Combined = {
                                    key: 'phase2_combined',
                                    name: '字段内容与一致性检查',
                                    thinking: '',
                                    status: 'completed',
                                    timestamp: phaseData.timestamp || '',
                                    order: 2
                                };
                            }
                            // 合并第二部分的思考内容
                            const groupName = phaseKey.includes('第1组') ? '第1组' : '第2组';
                            const thinkingContent = phaseData.ai_thinking || '无思考内容';
                            if (phase2Combined.thinking === '') {
                                phase2Combined.thinking = `### ${groupName}\n\n${thinkingContent}`;
                            } else {
                                phase2Combined.thinking += `\n\n---\n\n### ${groupName}\n\n${thinkingContent}`;
                            }
                            // 使用最新的时间戳
                            if (phaseData.timestamp && phaseData.timestamp > phase2Combined.timestamp) {
                                phase2Combined.timestamp = phaseData.timestamp;
                            }
                        } else if (phaseKey === 'phase3') {
                            processedPhases.push({
                                key: 'phase3',
                                name: phaseData.phase_name || '金额与标准检查',
                                thinking: phaseData.ai_thinking || '无思考内容',
                                status: phaseData.status || 'completed',
                                timestamp: phaseData.timestamp || '',
                                order: 3
                            });
                        } else if (phaseKey === 'phase4') {
                            processedPhases.push({
                                key: 'phase4',
                                name: phaseData.phase_name || '八项规定精神',
                                thinking: phaseData.ai_thinking || '无思考内容',
                                status: phaseData.status || 'completed',
                                timestamp: phaseData.timestamp || '',
                                order: 4
                            });
                        }
                    });

                    // 添加合并的第二部分
                    if (phase2Combined) {

                        processedPhases.push(phase2Combined);
                    }

                    // 按顺序排序
                    processedPhases.sort((a, b) => a.order - b.order);



                    contentHTML += '<div class="phases-container">';

                    processedPhases.forEach((phase, index) => {
                        const phaseNumber = index + 1;
                        const phaseName = phase.name;
                        const status = phase.status;
                        const timestamp = phase.timestamp;
                        const thinking = phase.thinking.trim();

                        contentHTML += `
                            <div class="thinking-phase" data-phase="${phase.key}">
                                <div class="phase-header" onclick="this.parentElement.querySelector('.phase-content').classList.toggle('expanded'); this.classList.toggle('expanded')">
                                    <div class="phase-title">
                                        阶段 ${phaseNumber}: ${this.escapeHtml(phaseName)}
                                    </div>
                                    <div class="phase-meta">
                                        <span class="phase-status ${status}">${this.getStatusText(status)}</span>
                                        <span class="phase-time">${this.formatTimestamp(timestamp)}</span>
                                        <span class="phase-expand-icon">▶</span>
                                    </div>
                                </div>
                                <div class="phase-content">
                                    <div class="thinking-text">${this.parseMarkdown(thinking)}</div>
                                </div>
                            </div>
                        `;
                    });

                    contentHTML += '</div>';
                }
            }

            thinkingContent.innerHTML = contentHTML;

            // 显示元数据
            this.displayThinkingMetadata(aiThinkingData.extraction_metadata);

        } catch (error) {
            console.error('加载AI思维链失败:', error);
            thinkingContent.innerHTML = `
                <div class="thinking-error">
                    <p>❌ 加载失败</p>
                    <p>错误信息: ${error.message}</p>
                </div>
            `;
        }
    }

    displayThinkingMetadata(metadata) {
        const metadataSection = document.getElementById('thinking-metadata');
        const metadataContent = document.getElementById('metadata-content');

        if (!metadata) {
            metadataSection.style.display = 'none';
            return;
        }

        let metadataHTML = '';

        const metadataItems = [
            { label: '提取时间', value: metadata.extracted_at },
            { label: '审核ID', value: metadata.audit_id },
            { label: '审核状态', value: metadata.audit_status },
            { label: '完成时间', value: metadata.completion_time },
            { label: '集成版本', value: metadata.integration_version || '1.0' }
        ];

        metadataItems.forEach(item => {
            if (item.value) {
                metadataHTML += `
                    <div class="metadata-item">
                        <div class="metadata-label">${item.label}</div>
                        <div class="metadata-value">${this.escapeHtml(item.value)}</div>
                    </div>
                `;
            }
        });

        metadataContent.innerHTML = metadataHTML;
        metadataSection.style.display = 'block';
    }

    expandAllPhases() {
        const phaseContents = document.querySelectorAll('.phase-content');
        const phaseHeaders = document.querySelectorAll('.phase-header');

        phaseContents.forEach(content => {
            content.classList.add('expanded');
        });

        phaseHeaders.forEach(header => {
            header.classList.add('expanded');
        });
    }

    collapseAllPhases() {
        const phaseContents = document.querySelectorAll('.phase-content');
        const phaseHeaders = document.querySelectorAll('.phase-header');

        phaseContents.forEach(content => {
            content.classList.remove('expanded');
        });

        phaseHeaders.forEach(header => {
            header.classList.remove('expanded');
        });
    }

    async copyThinkingContent() {
        try {
            const aiThinkingData = this.auditData.ai_thinking_chain;
            let textContent = '=== AI思考过程 ===\n\n';

            // 添加组合思维链
            if (aiThinkingData.combined_thinking) {
                // 检查combined_thinking是否缺少第二部分内容
                let combinedContent = aiThinkingData.combined_thinking;

                // 如果combined_thinking不包含第二部分内容，动态补充
                if (!combinedContent.includes('阶段 2/4') && aiThinkingData.phases_history) {
                    const phase2Group1 = aiThinkingData.phases_history['第二部分：字段内容与一致性检查_(第1组)'];
                    const phase2Group2 = aiThinkingData.phases_history['第二部分：字段内容与一致性检查_(第2组)'];

                    if (phase2Group1 || phase2Group2) {
                        // 找到阶段1结束和阶段3开始的位置
                        const stage1End = combinedContent.indexOf('---\n\n## 🔍 金额与标准检查');
                        if (stage1End !== -1) {
                            // 构建第二部分内容
                            let phase2Content = '\n\n---\n\n## 🔍 字段内容与一致性检查 (阶段 2/4)\n\n';

                            if (phase2Group1) {
                                phase2Content += '### 第1组 (规则6-14)\n\n';
                                phase2Content += phase2Group1.ai_thinking + '\n\n';
                            }

                            if (phase2Group2) {
                                phase2Content += '### 第2组 (规则15-24)\n\n';
                                phase2Content += phase2Group2.ai_thinking + '\n\n';
                            }

                            // 插入第二部分内容
                            combinedContent = combinedContent.substring(0, stage1End) +
                                            phase2Content +
                                            combinedContent.substring(stage1End);
                        }
                    }
                }

                textContent += '【完整思维链概览】\n';
                textContent += combinedContent + '\n\n';
            }

            // 添加各阶段思维链
            if (aiThinkingData.phases_history) {
                const phases = Object.entries(aiThinkingData.phases_history);

                // 按阶段顺序排序并合并第二部分
                const processedPhases = [];
                let phase2Combined = null;

                phases.forEach(([phaseKey, phaseData]) => {
                    if (phaseKey === 'phase1') {
                        processedPhases.push({
                            key: 'phase1',
                            name: phaseData.phase_name || '附件完整性检查',
                            thinking: phaseData.ai_thinking || '无思考内容',
                            order: 1
                        });
                    } else if (phaseKey.includes('第二部分')) {
                        if (!phase2Combined) {
                            phase2Combined = {
                                key: 'phase2_combined',
                                name: '字段内容与一致性检查',
                                thinking: '',
                                order: 2
                            };
                        }
                        // 合并第二部分的思考内容
                        const groupName = phaseKey.includes('第1组') ? '第1组' : '第2组';
                        const thinkingContent = phaseData.ai_thinking || '无思考内容';
                        if (phase2Combined.thinking === '') {
                            phase2Combined.thinking = `### ${groupName}\n\n${thinkingContent}`;
                        } else {
                            phase2Combined.thinking += `\n\n---\n\n### ${groupName}\n\n${thinkingContent}`;
                        }
                    } else if (phaseKey === 'phase3') {
                        processedPhases.push({
                            key: 'phase3',
                            name: phaseData.phase_name || '金额与标准检查',
                            thinking: phaseData.ai_thinking || '无思考内容',
                            order: 3
                        });
                    } else if (phaseKey === 'phase4') {
                        processedPhases.push({
                            key: 'phase4',
                            name: phaseData.phase_name || '八项规定精神',
                            thinking: phaseData.ai_thinking || '无思考内容',
                            order: 4
                        });
                    }
                });

                // 添加合并的第二部分
                if (phase2Combined) {
                    processedPhases.push(phase2Combined);
                }

                // 按顺序排序
                processedPhases.sort((a, b) => a.order - b.order);

                // 输出处理后的阶段
                processedPhases.forEach((phase, index) => {
                    const phaseNumber = index + 1;
                    textContent += `【阶段 ${phaseNumber}: ${phase.name}】\n`;
                    textContent += phase.thinking.trim() + '\n\n';
                });
            }

            // 添加元数据
            if (aiThinkingData.extraction_metadata) {
                textContent += '【元数据信息】\n';
                const metadata = aiThinkingData.extraction_metadata;
                textContent += `提取时间: ${metadata.extracted_at || 'N/A'}\n`;
                textContent += `审核ID: ${metadata.audit_id || 'N/A'}\n`;
                textContent += `审核状态: ${metadata.audit_status || 'N/A'}\n`;
                textContent += `完成时间: ${metadata.completion_time || 'N/A'}\n`;
            }

            await navigator.clipboard.writeText(textContent);
            this.showSuccessToast('AI思考过程已复制到剪贴板');

        } catch (error) {
            console.error('复制失败:', error);
            this.showSuccessToast('复制失败，请手动选择内容复制');
        }
    }

    searchThinkingContent() {
        const searchInput = document.getElementById('thinking-search-input');
        const searchResults = document.getElementById('search-results');
        const query = searchInput.value.trim().toLowerCase();

        if (query.length < 2) {
            this.clearSearchResults();
            return;
        }

        const aiThinkingData = this.auditData.ai_thinking_chain;
        if (!aiThinkingData) return;

        let results = [];

        // 搜索组合思维链
        if (aiThinkingData.combined_thinking) {
            const content = aiThinkingData.combined_thinking.toLowerCase();
            if (content.includes(query)) {
                const context = this.getSearchContext(aiThinkingData.combined_thinking, query);
                results.push({
                    type: 'combined',
                    title: '完整思维链概览',
                    context: context
                });
            }
        }

        // 搜索各阶段思维链
        if (aiThinkingData.phases_history) {
            const phases = Object.entries(aiThinkingData.phases_history);

            // 处理和合并阶段数据用于搜索
            const processedPhases = [];
            let phase2Combined = null;

            phases.forEach(([phaseKey, phaseData]) => {
                if (phaseKey === 'phase1') {
                    processedPhases.push({
                        key: 'phase1',
                        name: phaseData.phase_name || '附件完整性检查',
                        thinking: phaseData.ai_thinking || '无思考内容',
                        order: 1
                    });
                } else if (phaseKey.includes('第二部分')) {
                    if (!phase2Combined) {
                        phase2Combined = {
                            key: 'phase2_combined',
                            name: '字段内容与一致性检查',
                            thinking: '',
                            order: 2
                        };
                    }
                    // 合并第二部分的思考内容
                    const groupName = phaseKey.includes('第1组') ? '第1组' : '第2组';
                    const thinkingContent = phaseData.ai_thinking || '无思考内容';
                    if (phase2Combined.thinking === '') {
                        phase2Combined.thinking = `### ${groupName}\n\n${thinkingContent}`;
                    } else {
                        phase2Combined.thinking += `\n\n---\n\n### ${groupName}\n\n${thinkingContent}`;
                    }
                } else if (phaseKey === 'phase3') {
                    processedPhases.push({
                        key: 'phase3',
                        name: phaseData.phase_name || '金额与标准检查',
                        thinking: phaseData.ai_thinking || '无思考内容',
                        order: 3
                    });
                } else if (phaseKey === 'phase4') {
                    processedPhases.push({
                        key: 'phase4',
                        name: phaseData.phase_name || '八项规定精神',
                        thinking: phaseData.ai_thinking || '无思考内容',
                        order: 4
                    });
                }
            });

            // 添加合并的第二部分
            if (phase2Combined) {
                processedPhases.push(phase2Combined);
            }

            // 按顺序排序
            processedPhases.sort((a, b) => a.order - b.order);

            // 搜索处理后的阶段
            processedPhases.forEach((phase, index) => {
                const thinking = phase.thinking || '';
                const content = thinking.toLowerCase();

                if (content.includes(query)) {
                    const context = this.getSearchContext(thinking, query);
                    results.push({
                        type: 'phase',
                        phaseKey: phase.key,
                        title: `阶段 ${index + 1}: ${phase.name}`,
                        context: context
                    });
                }
            });
        }

        this.displaySearchResults(results, query);
    }

    getSearchContext(text, query, contextLength = 100) {
        const lowerText = text.toLowerCase();
        const lowerQuery = query.toLowerCase();
        const index = lowerText.indexOf(lowerQuery);

        if (index === -1) return '';

        const start = Math.max(0, index - contextLength);
        const end = Math.min(text.length, index + query.length + contextLength);

        let context = text.substring(start, end);

        if (start > 0) context = '...' + context;
        if (end < text.length) context = context + '...';

        return context;
    }

    displaySearchResults(results, query) {
        const searchResults = document.getElementById('search-results');

        if (results.length === 0) {
            searchResults.innerHTML = '<p style="color: var(--text-muted); text-align: center;">未找到相关内容</p>';
            searchResults.style.display = 'block';
            return;
        }

        let resultsHTML = '';
        results.forEach(result => {
            const highlightedContext = this.highlightSearchTerm(result.context, query);
            resultsHTML += `
                <div class="search-result-item" onclick="window.aiResultsViewer.scrollToResult('${result.type}', '${result.phaseKey || ''}')">
                    <div style="font-weight: 600; color: var(--accent-blue); margin-bottom: 5px;">
                        ${result.title}
                    </div>
                    <div class="search-result-text">${highlightedContext}</div>
                </div>
            `;
        });

        searchResults.innerHTML = resultsHTML;
        searchResults.style.display = 'block';
    }

    highlightSearchTerm(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }

    clearSearchResults() {
        const searchResults = document.getElementById('search-results');
        searchResults.style.display = 'none';
        searchResults.innerHTML = '';
    }

    // 辅助方法
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Markdown转HTML的简单解析器
    parseMarkdown(text) {
        if (!text) return '';

        let html = text;

        // 处理标题
        html = html.replace(/^## (.+)$/gm, '<h2 class="md-h2">$1</h2>');
        html = html.replace(/^### (.+)$/gm, '<h3 class="md-h3">$1</h3>');
        html = html.replace(/^#### (.+)$/gm, '<h4 class="md-h4">$1</h4>');

        // 处理粗体（支持中文）
        html = html.replace(/\*\*([^*]+?)\*\*/g, '<strong class="md-bold">$1</strong>');

        // 处理斜体（更保守的匹配，避免与粗体冲突）
        html = html.replace(/(?<!\*)\*([^*\n]+?)\*(?!\*)/g, '<em class="md-italic">$1</em>');

        // 处理代码块
        html = html.replace(/```([\s\S]*?)```/g, '<pre class="md-code-block"><code>$1</code></pre>');

        // 处理行内代码
        html = html.replace(/`([^`\n]+)`/g, '<code class="md-inline-code">$1</code>');

        // 处理无序列表（支持多级）
        html = html.replace(/^- (.+)$/gm, '<li class="md-list-item">$1</li>');

        // 将连续的列表项包装在ul中
        html = html.replace(/(<li class="md-list-item">.*?<\/li>\s*)+/gs, (match) => {
            return `<ul class="md-list">${match}</ul>`;
        });

        // 处理分隔线
        html = html.replace(/^---+$/gm, '<hr class="md-divider">');

        // 处理段落（按双换行分割）
        const sections = html.split(/\n\s*\n/);
        html = sections.map(section => {
            section = section.trim();
            if (!section) return '';

            // 如果已经是HTML标签，直接返回
            if (section.match(/^<(h[1-6]|ul|pre|hr)/)) {
                return section;
            }

            // 如果包含列表项但没有被ul包装，包装它
            if (section.includes('<li class="md-list-item">')) {
                return `<ul class="md-list">${section}</ul>`;
            }

            // 普通段落
            return `<p class="md-paragraph">${section.replace(/\n/g, '<br>')}</p>`;
        }).filter(p => p).join('\n');

        return html;
    }

    getStatusText(status) {
        const statusMap = {
            'completed': '已完成',
            'running': '进行中',
            'failed': '失败',
            'unknown': '未知'
        };
        return statusMap[status] || status;
    }

    formatTimestamp(timestamp) {
        if (!timestamp) return '';

        try {
            const date = new Date(timestamp);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            return timestamp;
        }
    }

    scrollToResult(type, phaseKey) {
        if (type === 'combined') {
            const combinedThinking = document.querySelector('.combined-thinking');
            if (combinedThinking) {
                combinedThinking.scrollIntoView({ behavior: 'smooth', block: 'center' });
                combinedThinking.style.animation = 'highlight 2s ease-out';
            }
        } else if (type === 'phase' && phaseKey) {
            const phaseElement = document.querySelector(`[data-phase="${phaseKey}"]`);
            if (phaseElement) {
                // 展开该阶段
                const phaseContent = phaseElement.querySelector('.phase-content');
                const phaseHeader = phaseElement.querySelector('.phase-header');

                if (phaseContent && phaseHeader) {
                    phaseContent.classList.add('expanded');
                    phaseHeader.classList.add('expanded');
                }

                // 滚动到该阶段
                phaseElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                phaseElement.style.animation = 'highlight 2s ease-out';
            }
        }

        // 清除搜索结果
        this.clearSearchResults();
    }
}

// 初始化结果查看器
document.addEventListener('DOMContentLoaded', () => {
    window.aiResultsViewer = new AIResultsViewer();
});

// 页面卸载时清理资源
window.addEventListener('beforeunload', () => {
    if (window.aiResultsViewer && window.aiResultsViewer.stopDynamicUpdates) {
        window.aiResultsViewer.stopDynamicUpdates();
        console.log('🧹 页面卸载，已清理动态更新资源');
    }
});
