<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>阶段性内容修复测试</title>
    <link rel="stylesheet" href="ai_console.css">
    <style>
        body {
            background: #0a0a0a;
            color: #ffffff;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .test-panel {
            background: #1a1a2e;
            border-radius: 10px;
            padding: 20px;
            border: 1px solid #00ffff;
        }
        
        .test-button {
            background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .test-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0, 255, 255, 0.3);
        }
        
        .status-info {
            background: #0a0a0a;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 11px;
        }
        
        .thinking-section {
            height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-panel">
            <h2>🧪 阶段性内容测试控制</h2>
            
            <div class="status-info">
                <strong>当前状态:</strong>
                <div id="currentStatus">未初始化</div>
            </div>
            
            <h3>模拟不同阶段</h3>
            <button class="test-button" onclick="simulatePhase(1, 20)">阶段1 (20%)</button>
            <button class="test-button" onclick="simulatePhase(2, 40)">阶段2 (40%)</button>
            <button class="test-button" onclick="simulatePhase(3, 60)">阶段3 (60%)</button>
            <button class="test-button" onclick="simulatePhase(4, 80)">阶段4 (80%)</button>
            <button class="test-button" onclick="simulateCompletion()">完成 (100%)</button>
            
            <h3>测试重复内容问题</h3>
            <button class="test-button" onclick="testRepetitiveContent()">模拟重复内容</button>
            <button class="test-button" onclick="testIncrementalUpdate()">测试增量更新</button>
            <button class="test-button" onclick="testSimilarContent()">测试相似内容</button>
            
            <h3>控制操作</h3>
            <button class="test-button" onclick="clearContent()">清空内容</button>
            <button class="test-button" onclick="resetTest()">重置测试</button>
            
            <div class="status-info">
                <strong>测试日志:</strong>
                <div id="testLogs" style="max-height: 150px; overflow-y: auto;"></div>
            </div>
        </div>
        
        <div class="test-panel">
            <h2>🤖 AI思考内容显示</h2>
            
            <div class="thinking-section">
                <div class="thinking-header">
                    <h3 class="thinking-title">🤖 AI分析引擎</h3>
                </div>
                <div class="thinking-content" id="thinking-content">
                    <div class="markdown-content">
                        <p>等待测试开始...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="js/state_manager.js"></script>
    <script src="ai_console_enhanced.js"></script>
    <script>
        let testConsole;
        let logCounter = 0;

        // 模拟的完整AI思考内容
        const fullAIContent = `## 🔍 附件完整性检查 (阶段 1/4)

## 🔍 附件完整性检查 (阶段详细分析)

首先，我需要理解我的角色：我是一个专业的财务审核助手，专注于附件完整性检查阶段。我必须严格按照提供的审核规则执行，只基于单据数据判断，逻辑清晰，输出格式规范。

当前阶段是【审核阶段：附件完整性检查】，规则有5条：
- 规则1：检查是否上传发票。
- 规则2：检查是否上传事前审批表。
- 规则3：检查是否上传用餐小票。
- 规则4：检查是否上传支付记录。
- 规则5：检查特殊物品签收表（有条件）。

✅ 第一阶段完成

---

## 🔍 字段内容与一致性检查 (阶段 2/4)

## 🔍 字段内容与一致性检查 (阶段详细分析)

当前阶段是【审核阶段：字段内容与一致性检查】，规则从6到24，共19条规则。

正在逐一检查每条规则...

✅ 第二阶段完成

---

## 🔍 金额与标准检查 (阶段 3/4)

## 🔍 金额与标准检查 (阶段详细分析)

当前阶段是【审核阶段：金额与标准检查】，规则从25到30，共6条规则。

正在检查金额相关规则...

✅ 第三阶段完成

---

## 🔍 八项规定合规性检查 (阶段 4/4)

## 🔍 八项规定合规性检查 (阶段详细分析)

当前阶段是【审核阶段：八项规定合规性检查】，规则从31到38，共8条规则。

正在检查合规性...

✅ 第四阶段完成

---

## 🎯 审核总结

所有审核阶段已完成，生成详细审核报告。`;

        // 初始化测试环境
        document.addEventListener('DOMContentLoaded', function() {
            testConsole = new AIConsoleEnhanced();
            testConsole.init();
            
            // 创建模拟状态管理器
            testConsole.stateManager = {
                currentState: {
                    audit_status: 'running',
                    current_phase: 'attachment',
                    progress_percent: 0,
                    ai_thinking: '',
                    message: '准备开始审核'
                }
            };
            
            addLog('测试环境初始化完成');
            updateStatus();
        });

        function addLog(message) {
            const logsContainer = document.getElementById('testLogs');
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${++logCounter}] ${new Date().toLocaleTimeString()} - ${message}`;
            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;
        }

        function updateStatus() {
            const statusDiv = document.getElementById('currentStatus');
            const state = testConsole.stateManager.currentState;
            statusDiv.innerHTML = `
                状态: ${state.audit_status}<br>
                阶段: ${state.current_phase}<br>
                进度: ${state.progress_percent}%<br>
                消息: ${state.message}
            `;
        }

        function simulatePhase(phase, progress) {
            addLog(`模拟阶段${phase}，进度${progress}%`);
            
            // 更新状态
            testConsole.stateManager.currentState = {
                audit_status: 'running',
                current_phase: `phase-${phase}`,
                progress_percent: progress,
                ai_thinking: fullAIContent,
                message: `正在执行第${phase}阶段`
            };
            
            // 触发AI思考更新
            testConsole.updateAIThinking(fullAIContent);
            updateStatus();
        }

        function simulateCompletion() {
            addLog('模拟审核完成');
            
            testConsole.stateManager.currentState = {
                audit_status: 'completed',
                current_phase: 'finished',
                progress_percent: 100,
                ai_thinking: fullAIContent,
                message: '审核完成',
                completion_time: new Date().toISOString()
            };
            
            testConsole.updateAIThinking(fullAIContent);
            updateStatus();
        }

        function testRepetitiveContent() {
            addLog('测试重复内容处理');
            
            // 模拟进度在80%但内容从第一阶段开始的情况
            testConsole.stateManager.currentState.progress_percent = 80;
            testConsole.stateManager.currentState.message = '金额与标准检查';
            
            testConsole.updateAIThinking(fullAIContent);
            updateStatus();
        }

        function testIncrementalUpdate() {
            addLog('测试增量更新');
            
            const baseContent = fullAIContent.substring(0, 500);
            const incrementalContent = fullAIContent.substring(0, 1000);
            
            // 先显示基础内容
            testConsole.updateAIThinking(baseContent);
            
            // 2秒后增量更新
            setTimeout(() => {
                testConsole.updateAIThinking(incrementalContent);
                addLog('增量更新完成');
            }, 2000);
        }

        function testSimilarContent() {
            addLog('测试相似内容处理');
            
            const similarContent = fullAIContent + '\n\n正在进行最终检查...';
            testConsole.updateAIThinking(similarContent);
        }

        function clearContent() {
            document.getElementById('thinking-content').innerHTML = '<div class="markdown-content"><p>内容已清空</p></div>';
            testConsole.lastThinkingText = '';
            addLog('内容已清空');
        }

        function resetTest() {
            clearContent();
            testConsole.stateManager.currentState = {
                audit_status: 'ready',
                current_phase: 'ready',
                progress_percent: 0,
                ai_thinking: '',
                message: '准备开始审核'
            };
            updateStatus();
            addLog('测试已重置');
        }
    </script>
</body>
</html>
