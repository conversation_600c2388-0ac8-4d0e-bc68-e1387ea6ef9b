// 测试合并规则解析功能
const testAiThinkingWithMergedRules = `
## 🔍 金额与标准检查 (阶段 3/4)

规则25：检查实际消费是否超预算。
- 状态：通过

规则26：检查酒水使用情况。
- 状态：通过

规则27-28：检查人均消费是否超标。
- 状态：通过

规则29：检查按人头计算的菜品超量。
- 状态：通过

规则30：检查天价菜。
- 状态：无法判断

## 🔍 八项规定精神 (阶段 4/4)

规则31：检查招待对象一致性。
- 状态：通过

规则32：检查公务招待的消费内容。
- 状态：通过

规则33：检查公务招待的来函。
- 状态：通过

规则34：检查是否存在奢侈或违规消费。
- 状态：通过

规则35：检查公务招待的住宿费用。
- 状态：通过

规则36-37：检查公务招待函件与报销信息一致性。
- 状态：通过

规则38：检查消费场所合规性。
- 状态：通过

**第二部分：审核结果**

JSON数组：
[
  {
    "rule_id": "规则27-28：检查人均消费是否超标。",
    "status": "通过",
    "reason": "人均消费170.55元，未超过550元标准。"
  },
  {
    "rule_id": "规则36-37：检查公务招待函件与报销信息一致性。",
    "status": "通过",
    "reason": "招待类型为商务招待，规则不适用。"
  }
]
`;

// 测试解析函数
function testMergedRulesProgress() {
    console.log('🧪 开始测试合并规则解析功能');
    
    // 规则范围定义
    const ruleRanges = {
        attachment: { start: 1, end: 5 },      // 规则1-5
        consistency: { start: 6, end: 24 },    // 规则6-24
        amount: { start: 25, end: 30 },        // 规则25-30
        compliance: { start: 31, end: 38 }     // 规则31-38
    };

    // 定义合并规则映射
    const mergedRules = {
        '27-28': [27, 28],  // 规则27-28算作规则27和28
        '36-37': [36, 37]   // 规则36-37算作规则36和37
    };
    
    // 初始化阶段进度
    const phaseProgress = {
        attachment: { completed: 0, total: 5 },
        consistency: { completed: 0, total: 19 },
        amount: { completed: 0, total: 6 },
        compliance: { completed: 0, total: 8 }
    };
    
    const completedRules = new Set();
    
    // 解析规则状态的正则表达式模式（包含合并规则）
    const rulePatterns = [
        /规则(\d+)：.*?状态.*?：\s*(通过|不通过|警告|无法判断)/gi,
        /规则(\d+-\d+)：.*?状态\s*[:：]\s*(通过|不通过|警告|无法判断)/gi,
        /"rule_id":\s*"规则(\d+-\d+)[^"]*"[^}]*"status":\s*"(通过|不通过|警告|无法判断)"/gi
    ];
    
    // 使用模式解析
    rulePatterns.forEach((pattern, index) => {
        let match;
        let patternMatches = 0;
        
        while ((match = pattern.exec(testAiThinkingWithMergedRules)) !== null) {
            const ruleId = match[1];
            const status = match[2];
            
            // 处理合并规则
            if (ruleId && ruleId.includes('-')) {
                const mergedRuleNumbers = mergedRules[ruleId];
                if (mergedRuleNumbers) {
                    mergedRuleNumbers.forEach(ruleNumber => {
                        completedRules.add(ruleNumber);
                        patternMatches++;
                        console.log(`📋 模式${index + 1}匹配到合并规则${ruleId}中的规则${ruleNumber}：${status}`);
                    });
                }
            } else {
                const ruleNumber = parseInt(ruleId);
                if (!isNaN(ruleNumber) && ruleNumber >= 1 && ruleNumber <= 38) {
                    completedRules.add(ruleNumber);
                    patternMatches++;
                    console.log(`📋 模式${index + 1}匹配到规则${ruleNumber}：${status}`);
                }
            }
        }
        
        if (patternMatches > 0) {
            console.log(`✅ 模式${index + 1}共匹配${patternMatches}条规则`);
        }
    });
    
    console.log('📊 总共解析到的规则:', Array.from(completedRules).sort((a, b) => a - b));
    
    // 根据完成的规则更新各阶段进度
    completedRules.forEach(ruleNumber => {
        for (const [phase, range] of Object.entries(ruleRanges)) {
            if (ruleNumber >= range.start && ruleNumber <= range.end) {
                phaseProgress[phase].completed++;
                break;
            }
        }
    });
    
    // 显示各阶段进度
    console.log('\n📈 各阶段进度统计:');
    for (const [phase, progress] of Object.entries(phaseProgress)) {
        const percentage = Math.round((progress.completed / progress.total) * 100);
        console.log(`${phase}: ${progress.completed}/${progress.total} (${percentage}%)`);
    }
    
    // 验证合并规则是否正确计算
    const expectedRules = [25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38];
    const actualRules = Array.from(completedRules).sort((a, b) => a - b);
    
    console.log('\n🔍 合并规则验证:');
    console.log('期望规则:', expectedRules);
    console.log('实际规则:', actualRules);
    console.log('是否匹配:', JSON.stringify(expectedRules) === JSON.stringify(actualRules));
    
    return {
        totalRules: completedRules.size,
        phaseProgress: phaseProgress,
        isCorrect: JSON.stringify(expectedRules) === JSON.stringify(actualRules)
    };
}

// 运行测试
const result = testMergedRulesProgress();
console.log(`\n🎯 测试结果: 共解析到 ${result.totalRules} 条规则`);
console.log(`✅ 合并规则处理${result.isCorrect ? '正确' : '错误'}`);
