// 测试进度解析功能
const testAiThinking = `
## 🔍 附件完整性检查 (阶段 1/4)

规则1：检查是否上传发票。
- 状态应为"通过"，因为要求满足。

规则2：检查是否上传事前审批表。
- 状态应为"通过"。

规则3：检查是否上传用餐小票。
- 状态应为"通过"。

规则4：检查是否上传支付记录。
- 状态应为"通过"。

规则5：检查特殊物品签收表。
- 状态应为"通过"（因为条件不满足）。

## 🔍 第二部分：字段内容与一致性检查 (阶段 2/4)

规则6：检查商务招待发起主体。
- 状态：通过

规则7：检查招待对象是否涉及公职人员。
- 状态：通过

规则8：检查招待发起主体一致性。
- 状态：通过

规则9：检查招待类型一致性。
- 状态：通过

规则10：检查招待日期一致性。
- 状态：通过

规则11：检查招待人数一致性。
- 状态：通过

规则12：检查陪餐人数一致性。
- 状态：通过

规则13：检查招待事由与项目状态。
- 状态：无法判断

规则14：检查项目相关性。
- 状态：警告

规则15：检查发票项目名称。
- 状态：通过

规则16：检查总人数一致性。
- 状态：通过

规则17：检查事前审批的及时性。
- 状态：通过

规则18：检查审批流程完整性。
- 状态：警告

规则19：检查审批落款日期。
- 状态：通过

规则20：检查招待日期与用餐日期一致性。
- 状态：通过

规则21：检查用餐日期与支付日期一致性。
- 状态：通过

规则22：检查小票与支付金额一致性。
- 状态：通过

规则23：检查报销与支付金额一致性。
- 状态：通过

规则24：检查发票开具的及时性。
- 状态：通过

## 🔍 金额与标准检查 (阶段 3/4)

规则25：检查实际消费是否超预算。
- 状态：通过

规则26：检查酒水使用情况。
- 状态：通过

规则27-28：检查人均消费是否超标。
- 状态：通过

规则29：检查按人头计算的菜品超量。
- 状态：通过

规则30：检查天价菜。
- 状态：无法判断

## 🔍 八项规定精神 (阶段 4/4)

规则31：检查招待对象一致性。
- 状态：通过

规则32：检查公务招待的消费内容。
- 状态：通过

规则33：检查公务招待的来函。
- 状态：通过

规则34：检查是否存在奢侈或违规消费。
- 状态：通过

规则35：检查公务招待的住宿费用。
- 状态：通过

规则36-37：检查公务招待函件与报销信息一致性。
- 状态：通过

规则38：检查消费场所合规性。
- 状态：通过
`;

// 测试解析函数
function testProgressParsing() {
    console.log('🧪 开始测试进度解析功能');
    
    // 规则范围定义
    const ruleRanges = {
        'attachment-check': { start: 1, end: 5, name: '附件完整性检查' },
        'content-check': { start: 6, end: 24, name: '字段内容与一致性检查' },
        'amount-check': { start: 25, end: 30, name: '金额与标准检查' },
        'compliance-check': { start: 31, end: 38, name: '八项规定合规性检查' }
    };
    
    // 初始化阶段进度
    const phaseProgress = {
        'attachment-check': { completed: 0, total: 5 },
        'content-check': { completed: 0, total: 19 },
        'amount-check': { completed: 0, total: 6 },
        'compliance-check': { completed: 0, total: 8 }
    };
    
    const completedRules = new Set();
    
    // 解析规则状态的正则表达式模式
    const rulePatterns = [
        /规则(\d+)：.*?状态.*?：\s*(通过|不通过|警告|无法判断)/gi,
        /规则(\d+)：.*?状态应为\s*["""]?(通过|不通过|警告|无法判断)["""]?/gi,
        /规则(\d+)：.*?应该为\s*["""]?(通过|不通过|警告|无法判断)["""]?/gi,
        /规则(\d+)：.*?status\s*[:：]\s*(通过|不通过|警告|无法判断)/gi,
        /-\s*规则(\d+):\s*(通过|不通过|警告|无法判断)/gi,
        /规则(\d+):\s*(通过|不通过|警告|无法判断)/gi,
        /规则(\d+)[\s\S]{0,200}?状态\s*[:：]\s*(通过|不通过|警告|无法判断)/gi
    ];
    
    // 专门解析"状态应为"格式的规则
    const shouldBePattern = /规则(\d+)[\s\S]{0,500}?状态应为\s*["""]?(通过|不通过|警告|无法判断)["""]?/gi;
    let shouldBeMatch;
    while ((shouldBeMatch = shouldBePattern.exec(testAiThinking)) !== null) {
        const ruleNumber = parseInt(shouldBeMatch[1]);
        const status = shouldBeMatch[2];
        if (!isNaN(ruleNumber) && ruleNumber >= 1 && ruleNumber <= 38) {
            completedRules.add(ruleNumber);
            console.log(`📋 "状态应为"格式匹配到规则${ruleNumber}：${status}`);
        }
    }
    
    // 使用其他模式解析
    rulePatterns.forEach((pattern, index) => {
        let match;
        let patternMatches = 0;
        
        while ((match = pattern.exec(testAiThinking)) !== null) {
            let ruleNumber, status;
            
            if (index < 7) {
                ruleNumber = parseInt(match[1]);
                status = match[2];
            } else {
                status = match[1];
                ruleNumber = parseInt(match[2]);
            }
            
            if (!isNaN(ruleNumber) && ruleNumber >= 1 && ruleNumber <= 38) {
                completedRules.add(ruleNumber);
                patternMatches++;
                console.log(`📋 模式${index + 1}匹配到规则${ruleNumber}：${status}`);
            }
        }
        
        if (patternMatches > 0) {
            console.log(`✅ 模式${index + 1}共匹配${patternMatches}条规则`);
        }
    });
    
    console.log('📊 总共解析到的规则:', Array.from(completedRules).sort((a, b) => a - b));
    
    // 根据完成的规则更新各阶段进度
    completedRules.forEach(ruleNumber => {
        for (const [phase, range] of Object.entries(ruleRanges)) {
            if (ruleNumber >= range.start && ruleNumber <= range.end) {
                phaseProgress[phase].completed++;
                break;
            }
        }
    });
    
    // 显示各阶段进度
    console.log('\n📈 各阶段进度统计:');
    for (const [phase, progress] of Object.entries(phaseProgress)) {
        const percentage = Math.round((progress.completed / progress.total) * 100);
        console.log(`${ruleRanges[phase].name}: ${progress.completed}/${progress.total} (${percentage}%)`);
    }
    
    return {
        totalRules: completedRules.size,
        phaseProgress: phaseProgress
    };
}

// 运行测试
const result = testProgressParsing();
console.log(`\n🎯 测试结果: 共解析到 ${result.totalRules} 条规则`);
